{"contractName": "<PERSON>a<PERSON><PERSON><PERSON>", "abi": [{"inputs": [], "payable": false, "stateMutability": "nonpayable", "type": "constructor"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "_from", "type": "address"}, {"indexed": true, "internalType": "address", "name": "_to", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "_value", "type": "uint256"}], "name": "Transfer", "type": "event"}, {"constant": true, "inputs": [], "name": "name", "outputs": [{"internalType": "string", "name": "", "type": "string"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": false, "inputs": [{"internalType": "address", "name": "receiver", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "send<PERSON>oin", "outputs": [{"internalType": "bool", "name": "sufficient", "type": "bool"}], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": false, "inputs": [{"internalType": "address", "name": "receiver", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "faucetCoin", "outputs": [{"internalType": "bool", "name": "sufficient", "type": "bool"}], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": true, "inputs": [{"internalType": "address", "name": "addr", "type": "address"}], "name": "getBalanceInEth", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": true, "inputs": [{"internalType": "address", "name": "addr", "type": "address"}], "name": "getBalance", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "payable": false, "stateMutability": "view", "type": "function"}], "metadata": "{\"compiler\":{\"version\":\"0.5.16+commit.9c3226ce\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"inputs\":[],\"payable\":false,\"stateMutability\":\"nonpayable\",\"type\":\"constructor\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"_from\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"_to\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"_value\",\"type\":\"uint256\"}],\"name\":\"Transfer\",\"type\":\"event\"},{\"constant\":false,\"inputs\":[{\"internalType\":\"address\",\"name\":\"receiver\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"amount\",\"type\":\"uint256\"}],\"name\":\"faucetCoin\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"sufficient\",\"type\":\"bool\"}],\"payable\":false,\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"constant\":true,\"inputs\":[{\"internalType\":\"address\",\"name\":\"addr\",\"type\":\"address\"}],\"name\":\"getBalance\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"payable\":false,\"stateMutability\":\"view\",\"type\":\"function\"},{\"constant\":true,\"inputs\":[{\"internalType\":\"address\",\"name\":\"addr\",\"type\":\"address\"}],\"name\":\"getBalanceInEth\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"payable\":false,\"stateMutability\":\"view\",\"type\":\"function\"},{\"constant\":true,\"inputs\":[],\"name\":\"name\",\"outputs\":[{\"internalType\":\"string\",\"name\":\"\",\"type\":\"string\"}],\"payable\":false,\"stateMutability\":\"view\",\"type\":\"function\"},{\"constant\":false,\"inputs\":[{\"internalType\":\"address\",\"name\":\"receiver\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"amount\",\"type\":\"uint256\"}],\"name\":\"sendCoin\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"sufficient\",\"type\":\"bool\"}],\"payable\":false,\"stateMutability\":\"nonpayable\",\"type\":\"function\"}],\"devdoc\":{\"methods\":{}},\"userdoc\":{\"methods\":{}}},\"settings\":{\"compilationTarget\":{\"project:/contracts/MetaCoin.sol\":\"MetaCoin\"},\"evmVersion\":\"istanbul\",\"libraries\":{},\"optimizer\":{\"enabled\":false,\"runs\":200},\"remappings\":[]},\"sources\":{\"project:/contracts/ConvertLib.sol\":{\"keccak256\":\"0x5d7ec656f0fdf72f9d62f1e53aa92cd31d5351798277bb675a3dfd01814a3b11\",\"urls\":[\"bzz-raw://b292485ebc918443632651831aef88f2c69062f108aea43e76312cd4d1f66d16\",\"dweb:/ipfs/QmXeoYaoeedo6pG21ufjYBkuSCWvr1baYsS4PQkx5xh4io\"]},\"project:/contracts/MetaCoin.sol\":{\"keccak256\":\"0x2c6669a75f5e8693a9d39cc177be728cc8cf3b5b47a62d345333663c219eef4e\",\"urls\":[\"bzz-raw://71def1dfba2aaab1ee1033810914bbe67a725b5fb3dd0b86e2dbd18ffdeb0567\",\"dweb:/ipfs/QmQ7FvYVEB5vVJyGPpQgzZiwLRYggmEJGzvnZ7fGqow4cy\"]}},\"version\":1}", "bytecode": "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__ConvertLib____________________________6396e4ee3d6103db846105b3565b60026040518363ffffffff1660e01b8152600401808381526020018281526020019250505060206040518083038186803b15801561041857600080fd5b505af415801561042c573d6000803e3d6000fd5b505050506040513d602081101561044257600080fd5b81019080805190602001909291905050509050919050565b6000816000803373ffffffffffffffffffffffffffffffffffffffff1673ffffffffffffffffffffffffffffffffffffffff1681526020019081526020016000205410156104ab57600090506105ad565b816000803373ffffffffffffffffffffffffffffffffffffffff1673ffffffffffffffffffffffffffffffffffffffff16815260200190815260200160002060008282540392505081905550816000808573ffffffffffffffffffffffffffffffffffffffff1673ffffffffffffffffffffffffffffffffffffffff168152602001908152602001600020600082825401925050819055508273ffffffffffffffffffffffffffffffffffffffff163373ffffffffffffffffffffffffffffffffffffffff167fddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef846040518082815260200191505060405180910390a3600190505b92915050565b60008060008373ffffffffffffffffffffffffffffffffffffffff1673ffffffffffffffffffffffffffffffffffffffff16815260200190815260200160002054905091905056fea265627a7a723158203013821c434cea863dcd58857daa73c7b698ea3bb5e6eba07b2e6da3fbbbc6d164736f6c63430005100032", "deployedBytecode": "0x608060405234801561001057600080fd5b50600436106100575760003560e01c806306fdde031461005c5780636c18bdee146100df5780637bd703e81461014557806390b98a111461019d578063f8b2cb4f14610203575b600080fd5b61006461025b565b6040518080602001828103825283818151815260200191508051906020019080838360005b838110156100a4578082015181840152602081019050610089565b50505050905090810190601f1680156100d15780820380516001836020036101000a031916815260200191505b509250505060405180910390f35b61012b600480360360408110156100f557600080fd5b81019080803573ffffffffffffffffffffffffffffffffffffffff169060200190929190803590602001909291905050506102f9565b604051808215151515815260200191505060405180910390f35b6101876004803603602081101561015b57600080fd5b81019080803573ffffffffffffffffffffffffffffffffffffffff1690602001909291905050506103b6565b6040518082815260200191505060405180910390f35b6101e9600480360360408110156101b357600080fd5b81019080803573ffffffffffffffffffffffffffffffffffffffff1690602001909291908035906020019092919050505061045a565b604051808215151515815260200191505060405180910390f35b6102456004803603602081101561021957600080fd5b81019080803573ffffffffffffffffffffffffffffffffffffffff1690602001909291905050506105b3565b6040518082815260200191505060405180910390f35b60018054600181600116156101000203166002900480601f0160208091040260200160405190810160405280929190818152602001828054600181600116156101000203166002900480156102f15780601f106102c6576101008083540402835291602001916102f1565b820191906000526020600020905b8154815290600101906020018083116102d457829003601f168201915b505050505081565b6000816000803373ffffffffffffffffffffffffffffffffffffffff1673ffffffffffffffffffffffffffffffffffffffff168152602001908152602001600020600082825401925050819055508273ffffffffffffffffffffffffffffffffffffffff163373ffffffffffffffffffffffffffffffffffffffff167fddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef846040518082815260200191505060405180910390a36001905092915050565b600073__ConvertLib____________________________6396e4ee3d6103db846105b3565b60026040518363ffffffff1660e01b8152600401808381526020018281526020019250505060206040518083038186803b15801561041857600080fd5b505af415801561042c573d6000803e3d6000fd5b505050506040513d602081101561044257600080fd5b81019080805190602001909291905050509050919050565b6000816000803373ffffffffffffffffffffffffffffffffffffffff1673ffffffffffffffffffffffffffffffffffffffff1681526020019081526020016000205410156104ab57600090506105ad565b816000803373ffffffffffffffffffffffffffffffffffffffff1673ffffffffffffffffffffffffffffffffffffffff16815260200190815260200160002060008282540392505081905550816000808573ffffffffffffffffffffffffffffffffffffffff1673ffffffffffffffffffffffffffffffffffffffff168152602001908152602001600020600082825401925050819055508273ffffffffffffffffffffffffffffffffffffffff163373ffffffffffffffffffffffffffffffffffffffff167fddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef846040518082815260200191505060405180910390a3600190505b92915050565b60008060008373ffffffffffffffffffffffffffffffffffffffff1673ffffffffffffffffffffffffffffffffffffffff16815260200190815260200160002054905091905056fea265627a7a723158203013821c434cea863dcd58857daa73c7b698ea3bb5e6eba07b2e6da3fbbbc6d164736f6c63430005100032", "sourceMap": "366:1104:1:-;;;594:37;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;519:67;8:9:-1;5:2;;;30:1;27;20:12;5:2;519:67:1;573:5;551:8;:19;560:9;551:19;;;;;;;;;;;;;;;:27;;;;366:1104;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;:::o;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;:::o;:::-;;;;;;;", "deployedSourceMap": "366:1104:1:-;;;;8:9:-1;5:2;;;30:1;27;20:12;5:2;366:1104:1;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;594:37;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;23:1:-1;8:100;33:3;30:1;27:10;8:100;;;99:1;94:3;90:11;84:18;80:1;75:3;71:11;64:39;52:2;49:1;45:10;40:15;;8:100;;;12:14;594:37:1;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;978:235;;;;;;13:2:-1;8:3;5:11;2:2;;;29:1;26;19:12;2:2;978:235:1;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;1221:134;;;;;;13:2:-1;8:3;5:11;2:2;;;29:1;26;19:12;2:2;1221:134:1;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;640:330;;;;;;13:2:-1;8:3;5:11;2:2;;;29:1;26;19:12;2:2;640:330:1;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;1363:104;;;;;;13:2:-1;8:3;5:11;2:2;;;29:1;26;19:12;2:2;1363:104:1;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;594:37;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::o;978:235::-;1066:15;1123:6;1099:8;:20;1108:10;1099:20;;;;;;;;;;;;;;;;:30;;;;;;;;;;;1166:8;1145:38;;1154:10;1145:38;;;1176:6;1145:38;;;;;;;;;;;;;;;;;;1201:4;1194:11;;978:235;;;;:::o;1221:134::-;1281:7;1308:10;:18;1327:16;1338:4;1327:10;:16::i;:::-;1345:1;1308:39;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8:9:-1;5:2;;;30:1;27;20:12;5:2;1308:39:1;;;;8:9:-1;5:2;;;45:16;42:1;39;24:38;77:16;74:1;67:27;5:2;1308:39:1;;;;;;;13:2:-1;8:3;5:11;2:2;;;29:1;26;19:12;2:2;1308:39:1;;;;;;;;;;;;;;;;1301:46;;1221:134;;;:::o;640:330::-;726:15;786:6;763:8;:20;772:10;763:20;;;;;;;;;;;;;;;;:29;759:47;;;801:5;794:12;;;;759:47;841:6;817:8;:20;826:10;817:20;;;;;;;;;;;;;;;;:30;;;;;;;;;;;880:6;858:8;:18;867:8;858:18;;;;;;;;;;;;;;;;:28;;;;;;;;;;;923:8;902:38;;911:10;902:38;;;933:6;902:38;;;;;;;;;;;;;;;;;;958:4;951:11;;640:330;;;;;:::o;1363:104::-;1418:7;1445:8;:14;1454:4;1445:14;;;;;;;;;;;;;;;;1438:21;;1363:104;;;:::o", "source": "// SPDX-License-Identifier: MIT\r\npragma solidity >=0.4.25 <0.7.0;\r\n\r\nimport \"./ConvertLib.sol\";\r\n\r\n// This is just a simple example of a coin-like contract.\r\n// It is not standards compatible and cannot be expected to talk to other\r\n// coin/token contracts. If you want to create a standards-compliant\r\n// token, see: https://github.com/ConsenSys/Tokens. Cheers!\r\n\r\ncontract MetaCoin {\r\n    mapping(address => uint256) balances;\r\n\r\n    event Transfer(address indexed _from, address indexed _to, uint256 _value);\r\n\r\n    constructor() public {\r\n        balances[tx.origin] = 10000;\r\n    }\r\n\r\n    string public name = \"Test Coin Nice\";\r\n\r\n    function sendCoin(address receiver, uint256 amount)\r\n        public\r\n        returns (bool sufficient)\r\n    {\r\n        if (balances[msg.sender] < amount) return false;\r\n        balances[msg.sender] -= amount;\r\n        balances[receiver] += amount;\r\n        emit Transfer(msg.sender, receiver, amount);\r\n        return true;\r\n    }\r\n\r\n    function faucetCoin(address receiver, uint256 amount)\r\n        public\r\n        returns (bool sufficient)\r\n    {\r\n        balances[msg.sender] += amount;\r\n        emit Transfer(msg.sender, receiver, amount);\r\n        return true;\r\n    }\r\n\r\n    function getBalanceInEth(address addr) public view returns (uint256) {\r\n        return ConvertLib.convert(getBalance(addr), 2);\r\n    }\r\n\r\n    function getBalance(address addr) public view returns (uint256) {\r\n        return balances[addr];\r\n    }\r\n}\r\n", "sourcePath": "C:\\Work\\ethereum-boilerplate\\Truffle\\contracts\\MetaCoin.sol", "ast": {"absolutePath": "project:/contracts/MetaCoin.sol", "exportedSymbols": {"MetaCoin": [141]}, "id": 142, "nodeType": "SourceUnit", "nodes": [{"id": 18, "literals": ["solidity", ">=", "0.4", ".25", "<", "0.7", ".0"], "nodeType": "PragmaDirective", "src": "33:32:1"}, {"absolutePath": "project:/contracts/ConvertLib.sol", "file": "./ConvertLib.sol", "id": 19, "nodeType": "ImportDirective", "scope": 142, "sourceUnit": 17, "src": "69:26:1", "symbolAliases": [], "unitAlias": ""}, {"baseContracts": [], "contractDependencies": [], "contractKind": "contract", "documentation": null, "fullyImplemented": true, "id": 141, "linearizedBaseContracts": [141], "name": "<PERSON>a<PERSON><PERSON><PERSON>", "nodeType": "ContractDefinition", "nodes": [{"constant": false, "id": 23, "name": "balances", "nodeType": "VariableDeclaration", "scope": 141, "src": "391:36:1", "stateVariable": true, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_address_$_t_uint256_$", "typeString": "mapping(address => uint256)"}, "typeName": {"id": 22, "keyType": {"id": 20, "name": "address", "nodeType": "ElementaryTypeName", "src": "399:7:1", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "nodeType": "Mapping", "src": "391:27:1", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_address_$_t_uint256_$", "typeString": "mapping(address => uint256)"}, "valueType": {"id": 21, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "410:7:1", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}}, "value": null, "visibility": "internal"}, {"anonymous": false, "documentation": null, "id": 31, "name": "Transfer", "nodeType": "EventDefinition", "parameters": {"id": 30, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 25, "indexed": true, "name": "_from", "nodeType": "VariableDeclaration", "scope": 31, "src": "451:21:1", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 24, "name": "address", "nodeType": "ElementaryTypeName", "src": "451:7:1", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "value": null, "visibility": "internal"}, {"constant": false, "id": 27, "indexed": true, "name": "_to", "nodeType": "VariableDeclaration", "scope": 31, "src": "474:19:1", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 26, "name": "address", "nodeType": "ElementaryTypeName", "src": "474:7:1", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "value": null, "visibility": "internal"}, {"constant": false, "id": 29, "indexed": false, "name": "_value", "nodeType": "VariableDeclaration", "scope": 31, "src": "495:14:1", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 28, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "495:7:1", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "value": null, "visibility": "internal"}], "src": "450:60:1"}, "src": "436:75:1"}, {"body": {"id": 41, "nodeType": "Block", "src": "540:46:1", "statements": [{"expression": {"argumentTypes": null, "id": 39, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftHandSide": {"argumentTypes": null, "baseExpression": {"argumentTypes": null, "id": 34, "name": "balances", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 23, "src": "551:8:1", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_address_$_t_uint256_$", "typeString": "mapping(address => uint256)"}}, "id": 37, "indexExpression": {"argumentTypes": null, "expression": {"argumentTypes": null, "id": 35, "name": "tx", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 205, "src": "560:2:1", "typeDescriptions": {"typeIdentifier": "t_magic_transaction", "typeString": "tx"}}, "id": 36, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberName": "origin", "nodeType": "MemberAccess", "referencedDeclaration": null, "src": "560:9:1", "typeDescriptions": {"typeIdentifier": "t_address_payable", "typeString": "address payable"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": true, "nodeType": "IndexAccess", "src": "551:19:1", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "Assignment", "operator": "=", "rightHandSide": {"argumentTypes": null, "hexValue": "3130303030", "id": 38, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "573:5:1", "subdenomination": null, "typeDescriptions": {"typeIdentifier": "t_rational_10000_by_1", "typeString": "int_const 10000"}, "value": "10000"}, "src": "551:27:1", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "id": 40, "nodeType": "ExpressionStatement", "src": "551:27:1"}]}, "documentation": null, "id": 42, "implemented": true, "kind": "constructor", "modifiers": [], "name": "", "nodeType": "FunctionDefinition", "parameters": {"id": 32, "nodeType": "ParameterList", "parameters": [], "src": "530:2:1"}, "returnParameters": {"id": 33, "nodeType": "ParameterList", "parameters": [], "src": "540:0:1"}, "scope": 141, "src": "519:67:1", "stateMutability": "nonpayable", "superFunction": null, "visibility": "public"}, {"constant": false, "id": 45, "name": "name", "nodeType": "VariableDeclaration", "scope": 141, "src": "594:37:1", "stateVariable": true, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_string_storage", "typeString": "string"}, "typeName": {"id": 43, "name": "string", "nodeType": "ElementaryTypeName", "src": "594:6:1", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "value": {"argumentTypes": null, "hexValue": "5465737420436f696e204e696365", "id": 44, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "615:16:1", "subdenomination": null, "typeDescriptions": {"typeIdentifier": "t_stringliteral_46a0f38d1072bfcd3788207df3bfe3ab87d3e14db0b129ab05a8932cf53fa9b3", "typeString": "literal_string \"Test Coin Nice\""}, "value": "Test Coin Nice"}, "visibility": "public"}, {"body": {"id": 85, "nodeType": "Block", "src": "748:222:1", "statements": [{"condition": {"argumentTypes": null, "commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 59, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"argumentTypes": null, "baseExpression": {"argumentTypes": null, "id": 54, "name": "balances", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 23, "src": "763:8:1", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_address_$_t_uint256_$", "typeString": "mapping(address => uint256)"}}, "id": 57, "indexExpression": {"argumentTypes": null, "expression": {"argumentTypes": null, "id": 55, "name": "msg", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 193, "src": "772:3:1", "typeDescriptions": {"typeIdentifier": "t_magic_message", "typeString": "msg"}}, "id": 56, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberName": "sender", "nodeType": "MemberAccess", "referencedDeclaration": null, "src": "772:10:1", "typeDescriptions": {"typeIdentifier": "t_address_payable", "typeString": "address payable"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "nodeType": "IndexAccess", "src": "763:20:1", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": "<", "rightExpression": {"argumentTypes": null, "id": 58, "name": "amount", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 49, "src": "786:6:1", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "763:29:1", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "falseBody": null, "id": 62, "nodeType": "IfStatement", "src": "759:47:1", "trueBody": {"expression": {"argumentTypes": null, "hexValue": "66616c7365", "id": 60, "isConstant": false, "isLValue": false, "isPure": true, "kind": "bool", "lValueRequested": false, "nodeType": "Literal", "src": "801:5:1", "subdenomination": null, "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}, "value": "false"}, "functionReturnParameters": 53, "id": 61, "nodeType": "Return", "src": "794:12:1"}}, {"expression": {"argumentTypes": null, "id": 68, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftHandSide": {"argumentTypes": null, "baseExpression": {"argumentTypes": null, "id": 63, "name": "balances", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 23, "src": "817:8:1", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_address_$_t_uint256_$", "typeString": "mapping(address => uint256)"}}, "id": 66, "indexExpression": {"argumentTypes": null, "expression": {"argumentTypes": null, "id": 64, "name": "msg", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 193, "src": "826:3:1", "typeDescriptions": {"typeIdentifier": "t_magic_message", "typeString": "msg"}}, "id": 65, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberName": "sender", "nodeType": "MemberAccess", "referencedDeclaration": null, "src": "826:10:1", "typeDescriptions": {"typeIdentifier": "t_address_payable", "typeString": "address payable"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": true, "nodeType": "IndexAccess", "src": "817:20:1", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "Assignment", "operator": "-=", "rightHandSide": {"argumentTypes": null, "id": 67, "name": "amount", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 49, "src": "841:6:1", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "817:30:1", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "id": 69, "nodeType": "ExpressionStatement", "src": "817:30:1"}, {"expression": {"argumentTypes": null, "id": 74, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftHandSide": {"argumentTypes": null, "baseExpression": {"argumentTypes": null, "id": 70, "name": "balances", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 23, "src": "858:8:1", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_address_$_t_uint256_$", "typeString": "mapping(address => uint256)"}}, "id": 72, "indexExpression": {"argumentTypes": null, "id": 71, "name": "receiver", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 47, "src": "867:8:1", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": true, "nodeType": "IndexAccess", "src": "858:18:1", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "Assignment", "operator": "+=", "rightHandSide": {"argumentTypes": null, "id": 73, "name": "amount", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 49, "src": "880:6:1", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "858:28:1", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "id": 75, "nodeType": "ExpressionStatement", "src": "858:28:1"}, {"eventCall": {"argumentTypes": null, "arguments": [{"argumentTypes": null, "expression": {"argumentTypes": null, "id": 77, "name": "msg", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 193, "src": "911:3:1", "typeDescriptions": {"typeIdentifier": "t_magic_message", "typeString": "msg"}}, "id": 78, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberName": "sender", "nodeType": "MemberAccess", "referencedDeclaration": null, "src": "911:10:1", "typeDescriptions": {"typeIdentifier": "t_address_payable", "typeString": "address payable"}}, {"argumentTypes": null, "id": 79, "name": "receiver", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 47, "src": "923:8:1", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, {"argumentTypes": null, "id": 80, "name": "amount", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 49, "src": "933:6:1", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_address_payable", "typeString": "address payable"}, {"typeIdentifier": "t_address", "typeString": "address"}, {"typeIdentifier": "t_uint256", "typeString": "uint256"}], "id": 76, "name": "Transfer", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 31, "src": "902:8:1", "typeDescriptions": {"typeIdentifier": "t_function_event_nonpayable$_t_address_$_t_address_$_t_uint256_$returns$__$", "typeString": "function (address,address,uint256)"}}, "id": 81, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "names": [], "nodeType": "FunctionCall", "src": "902:38:1", "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 82, "nodeType": "EmitStatement", "src": "897:43:1"}, {"expression": {"argumentTypes": null, "hexValue": "74727565", "id": 83, "isConstant": false, "isLValue": false, "isPure": true, "kind": "bool", "lValueRequested": false, "nodeType": "Literal", "src": "958:4:1", "subdenomination": null, "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}, "value": "true"}, "functionReturnParameters": 53, "id": 84, "nodeType": "Return", "src": "951:11:1"}]}, "documentation": null, "id": 86, "implemented": true, "kind": "function", "modifiers": [], "name": "send<PERSON>oin", "nodeType": "FunctionDefinition", "parameters": {"id": 50, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 47, "name": "receiver", "nodeType": "VariableDeclaration", "scope": 86, "src": "658:16:1", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 46, "name": "address", "nodeType": "ElementaryTypeName", "src": "658:7:1", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "value": null, "visibility": "internal"}, {"constant": false, "id": 49, "name": "amount", "nodeType": "VariableDeclaration", "scope": 86, "src": "676:14:1", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 48, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "676:7:1", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "value": null, "visibility": "internal"}], "src": "657:34:1"}, "returnParameters": {"id": 53, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 52, "name": "sufficient", "nodeType": "VariableDeclaration", "scope": 86, "src": "726:15:1", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}, "typeName": {"id": 51, "name": "bool", "nodeType": "ElementaryTypeName", "src": "726:4:1", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "value": null, "visibility": "internal"}], "src": "725:17:1"}, "scope": 141, "src": "640:330:1", "stateMutability": "nonpayable", "superFunction": null, "visibility": "public"}, {"body": {"id": 111, "nodeType": "Block", "src": "1088:125:1", "statements": [{"expression": {"argumentTypes": null, "id": 100, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftHandSide": {"argumentTypes": null, "baseExpression": {"argumentTypes": null, "id": 95, "name": "balances", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 23, "src": "1099:8:1", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_address_$_t_uint256_$", "typeString": "mapping(address => uint256)"}}, "id": 98, "indexExpression": {"argumentTypes": null, "expression": {"argumentTypes": null, "id": 96, "name": "msg", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 193, "src": "1108:3:1", "typeDescriptions": {"typeIdentifier": "t_magic_message", "typeString": "msg"}}, "id": 97, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberName": "sender", "nodeType": "MemberAccess", "referencedDeclaration": null, "src": "1108:10:1", "typeDescriptions": {"typeIdentifier": "t_address_payable", "typeString": "address payable"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": true, "nodeType": "IndexAccess", "src": "1099:20:1", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "Assignment", "operator": "+=", "rightHandSide": {"argumentTypes": null, "id": 99, "name": "amount", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 90, "src": "1123:6:1", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "1099:30:1", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "id": 101, "nodeType": "ExpressionStatement", "src": "1099:30:1"}, {"eventCall": {"argumentTypes": null, "arguments": [{"argumentTypes": null, "expression": {"argumentTypes": null, "id": 103, "name": "msg", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 193, "src": "1154:3:1", "typeDescriptions": {"typeIdentifier": "t_magic_message", "typeString": "msg"}}, "id": 104, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberName": "sender", "nodeType": "MemberAccess", "referencedDeclaration": null, "src": "1154:10:1", "typeDescriptions": {"typeIdentifier": "t_address_payable", "typeString": "address payable"}}, {"argumentTypes": null, "id": 105, "name": "receiver", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 88, "src": "1166:8:1", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, {"argumentTypes": null, "id": 106, "name": "amount", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 90, "src": "1176:6:1", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_address_payable", "typeString": "address payable"}, {"typeIdentifier": "t_address", "typeString": "address"}, {"typeIdentifier": "t_uint256", "typeString": "uint256"}], "id": 102, "name": "Transfer", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 31, "src": "1145:8:1", "typeDescriptions": {"typeIdentifier": "t_function_event_nonpayable$_t_address_$_t_address_$_t_uint256_$returns$__$", "typeString": "function (address,address,uint256)"}}, "id": 107, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "names": [], "nodeType": "FunctionCall", "src": "1145:38:1", "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 108, "nodeType": "EmitStatement", "src": "1140:43:1"}, {"expression": {"argumentTypes": null, "hexValue": "74727565", "id": 109, "isConstant": false, "isLValue": false, "isPure": true, "kind": "bool", "lValueRequested": false, "nodeType": "Literal", "src": "1201:4:1", "subdenomination": null, "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}, "value": "true"}, "functionReturnParameters": 94, "id": 110, "nodeType": "Return", "src": "1194:11:1"}]}, "documentation": null, "id": 112, "implemented": true, "kind": "function", "modifiers": [], "name": "faucetCoin", "nodeType": "FunctionDefinition", "parameters": {"id": 91, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 88, "name": "receiver", "nodeType": "VariableDeclaration", "scope": 112, "src": "998:16:1", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 87, "name": "address", "nodeType": "ElementaryTypeName", "src": "998:7:1", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "value": null, "visibility": "internal"}, {"constant": false, "id": 90, "name": "amount", "nodeType": "VariableDeclaration", "scope": 112, "src": "1016:14:1", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 89, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "1016:7:1", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "value": null, "visibility": "internal"}], "src": "997:34:1"}, "returnParameters": {"id": 94, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 93, "name": "sufficient", "nodeType": "VariableDeclaration", "scope": 112, "src": "1066:15:1", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}, "typeName": {"id": 92, "name": "bool", "nodeType": "ElementaryTypeName", "src": "1066:4:1", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "value": null, "visibility": "internal"}], "src": "1065:17:1"}, "scope": 141, "src": "978:235:1", "stateMutability": "nonpayable", "superFunction": null, "visibility": "public"}, {"body": {"id": 127, "nodeType": "Block", "src": "1290:65:1", "statements": [{"expression": {"argumentTypes": null, "arguments": [{"argumentTypes": null, "arguments": [{"argumentTypes": null, "id": 122, "name": "addr", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 114, "src": "1338:4:1", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_address", "typeString": "address"}], "id": 121, "name": "getBalance", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 140, "src": "1327:10:1", "typeDescriptions": {"typeIdentifier": "t_function_internal_view$_t_address_$returns$_t_uint256_$", "typeString": "function (address) view returns (uint256)"}}, "id": 123, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "names": [], "nodeType": "FunctionCall", "src": "1327:16:1", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, {"argumentTypes": null, "hexValue": "32", "id": 124, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "1345:1:1", "subdenomination": null, "typeDescriptions": {"typeIdentifier": "t_rational_2_by_1", "typeString": "int_const 2"}, "value": "2"}], "expression": {"argumentTypes": [{"typeIdentifier": "t_uint256", "typeString": "uint256"}, {"typeIdentifier": "t_rational_2_by_1", "typeString": "int_const 2"}], "expression": {"argumentTypes": null, "id": 119, "name": "ConvertLib", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 16, "src": "1308:10:1", "typeDescriptions": {"typeIdentifier": "t_type$_t_contract$_ConvertLib_$16_$", "typeString": "type(library ConvertLib)"}}, "id": 120, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberName": "convert", "nodeType": "MemberAccess", "referencedDeclaration": 15, "src": "1308:18:1", "typeDescriptions": {"typeIdentifier": "t_function_delegatecall_pure$_t_uint256_$_t_uint256_$returns$_t_uint256_$", "typeString": "function (uint256,uint256) pure returns (uint256)"}}, "id": 125, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "names": [], "nodeType": "FunctionCall", "src": "1308:39:1", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "functionReturnParameters": 118, "id": 126, "nodeType": "Return", "src": "1301:46:1"}]}, "documentation": null, "id": 128, "implemented": true, "kind": "function", "modifiers": [], "name": "getBalanceInEth", "nodeType": "FunctionDefinition", "parameters": {"id": 115, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 114, "name": "addr", "nodeType": "VariableDeclaration", "scope": 128, "src": "1246:12:1", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 113, "name": "address", "nodeType": "ElementaryTypeName", "src": "1246:7:1", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "value": null, "visibility": "internal"}], "src": "1245:14:1"}, "returnParameters": {"id": 118, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 117, "name": "", "nodeType": "VariableDeclaration", "scope": 128, "src": "1281:7:1", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 116, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "1281:7:1", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "value": null, "visibility": "internal"}], "src": "1280:9:1"}, "scope": 141, "src": "1221:134:1", "stateMutability": "view", "superFunction": null, "visibility": "public"}, {"body": {"id": 139, "nodeType": "Block", "src": "1427:40:1", "statements": [{"expression": {"argumentTypes": null, "baseExpression": {"argumentTypes": null, "id": 135, "name": "balances", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 23, "src": "1445:8:1", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_address_$_t_uint256_$", "typeString": "mapping(address => uint256)"}}, "id": 137, "indexExpression": {"argumentTypes": null, "id": 136, "name": "addr", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 130, "src": "1454:4:1", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "nodeType": "IndexAccess", "src": "1445:14:1", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "functionReturnParameters": 134, "id": 138, "nodeType": "Return", "src": "1438:21:1"}]}, "documentation": null, "id": 140, "implemented": true, "kind": "function", "modifiers": [], "name": "getBalance", "nodeType": "FunctionDefinition", "parameters": {"id": 131, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 130, "name": "addr", "nodeType": "VariableDeclaration", "scope": 140, "src": "1383:12:1", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 129, "name": "address", "nodeType": "ElementaryTypeName", "src": "1383:7:1", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "value": null, "visibility": "internal"}], "src": "1382:14:1"}, "returnParameters": {"id": 134, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 133, "name": "", "nodeType": "VariableDeclaration", "scope": 140, "src": "1418:7:1", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 132, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "1418:7:1", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "value": null, "visibility": "internal"}], "src": "1417:9:1"}, "scope": 141, "src": "1363:104:1", "stateMutability": "view", "superFunction": null, "visibility": "public"}], "scope": 142, "src": "366:1104:1"}], "src": "33:1439:1"}, "legacyAST": {"attributes": {"absolutePath": "project:/contracts/MetaCoin.sol", "exportedSymbols": {"MetaCoin": [141]}}, "children": [{"attributes": {"literals": ["solidity", ">=", "0.4", ".25", "<", "0.7", ".0"]}, "id": 18, "name": "PragmaDirective", "src": "33:32:1"}, {"attributes": {"SourceUnit": 17, "absolutePath": "project:/contracts/ConvertLib.sol", "file": "./ConvertLib.sol", "scope": 142, "symbolAliases": [null], "unitAlias": ""}, "id": 19, "name": "ImportDirective", "src": "69:26:1"}, {"attributes": {"baseContracts": [null], "contractDependencies": [null], "contractKind": "contract", "documentation": null, "fullyImplemented": true, "linearizedBaseContracts": [141], "name": "<PERSON>a<PERSON><PERSON><PERSON>", "scope": 142}, "children": [{"attributes": {"constant": false, "name": "balances", "scope": 141, "stateVariable": true, "storageLocation": "default", "type": "mapping(address => uint256)", "value": null, "visibility": "internal"}, "children": [{"attributes": {"type": "mapping(address => uint256)"}, "children": [{"attributes": {"name": "address", "type": "address"}, "id": 20, "name": "ElementaryTypeName", "src": "399:7:1"}, {"attributes": {"name": "uint256", "type": "uint256"}, "id": 21, "name": "ElementaryTypeName", "src": "410:7:1"}], "id": 22, "name": "Mapping", "src": "391:27:1"}], "id": 23, "name": "VariableDeclaration", "src": "391:36:1"}, {"attributes": {"anonymous": false, "documentation": null, "name": "Transfer"}, "children": [{"children": [{"attributes": {"constant": false, "indexed": true, "name": "_from", "scope": 31, "stateVariable": false, "storageLocation": "default", "type": "address", "value": null, "visibility": "internal"}, "children": [{"attributes": {"name": "address", "stateMutability": "nonpayable", "type": "address"}, "id": 24, "name": "ElementaryTypeName", "src": "451:7:1"}], "id": 25, "name": "VariableDeclaration", "src": "451:21:1"}, {"attributes": {"constant": false, "indexed": true, "name": "_to", "scope": 31, "stateVariable": false, "storageLocation": "default", "type": "address", "value": null, "visibility": "internal"}, "children": [{"attributes": {"name": "address", "stateMutability": "nonpayable", "type": "address"}, "id": 26, "name": "ElementaryTypeName", "src": "474:7:1"}], "id": 27, "name": "VariableDeclaration", "src": "474:19:1"}, {"attributes": {"constant": false, "indexed": false, "name": "_value", "scope": 31, "stateVariable": false, "storageLocation": "default", "type": "uint256", "value": null, "visibility": "internal"}, "children": [{"attributes": {"name": "uint256", "type": "uint256"}, "id": 28, "name": "ElementaryTypeName", "src": "495:7:1"}], "id": 29, "name": "VariableDeclaration", "src": "495:14:1"}], "id": 30, "name": "ParameterList", "src": "450:60:1"}], "id": 31, "name": "EventDefinition", "src": "436:75:1"}, {"attributes": {"documentation": null, "implemented": true, "isConstructor": true, "kind": "constructor", "modifiers": [null], "name": "", "scope": 141, "stateMutability": "nonpayable", "superFunction": null, "visibility": "public"}, "children": [{"attributes": {"parameters": [null]}, "children": [], "id": 32, "name": "ParameterList", "src": "530:2:1"}, {"attributes": {"parameters": [null]}, "children": [], "id": 33, "name": "ParameterList", "src": "540:0:1"}, {"children": [{"children": [{"attributes": {"argumentTypes": null, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "operator": "=", "type": "uint256"}, "children": [{"attributes": {"argumentTypes": null, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": true, "type": "uint256"}, "children": [{"attributes": {"argumentTypes": null, "overloadedDeclarations": [null], "referencedDeclaration": 23, "type": "mapping(address => uint256)", "value": "balances"}, "id": 34, "name": "Identifier", "src": "551:8:1"}, {"attributes": {"argumentTypes": null, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "member_name": "origin", "referencedDeclaration": null, "type": "address payable"}, "children": [{"attributes": {"argumentTypes": null, "overloadedDeclarations": [null], "referencedDeclaration": 205, "type": "tx", "value": "tx"}, "id": 35, "name": "Identifier", "src": "560:2:1"}], "id": 36, "name": "MemberAccess", "src": "560:9:1"}], "id": 37, "name": "IndexAccess", "src": "551:19:1"}, {"attributes": {"argumentTypes": null, "hexvalue": "3130303030", "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "subdenomination": null, "token": "number", "type": "int_const 10000", "value": "10000"}, "id": 38, "name": "Literal", "src": "573:5:1"}], "id": 39, "name": "Assignment", "src": "551:27:1"}], "id": 40, "name": "ExpressionStatement", "src": "551:27:1"}], "id": 41, "name": "Block", "src": "540:46:1"}], "id": 42, "name": "FunctionDefinition", "src": "519:67:1"}, {"attributes": {"constant": false, "name": "name", "scope": 141, "stateVariable": true, "storageLocation": "default", "type": "string", "visibility": "public"}, "children": [{"attributes": {"name": "string", "type": "string"}, "id": 43, "name": "ElementaryTypeName", "src": "594:6:1"}, {"attributes": {"argumentTypes": null, "hexvalue": "5465737420436f696e204e696365", "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "subdenomination": null, "token": "string", "type": "literal_string \"Test Coin Nice\"", "value": "Test Coin Nice"}, "id": 44, "name": "Literal", "src": "615:16:1"}], "id": 45, "name": "VariableDeclaration", "src": "594:37:1"}, {"attributes": {"documentation": null, "implemented": true, "isConstructor": false, "kind": "function", "modifiers": [null], "name": "send<PERSON>oin", "scope": 141, "stateMutability": "nonpayable", "superFunction": null, "visibility": "public"}, "children": [{"children": [{"attributes": {"constant": false, "name": "receiver", "scope": 86, "stateVariable": false, "storageLocation": "default", "type": "address", "value": null, "visibility": "internal"}, "children": [{"attributes": {"name": "address", "stateMutability": "nonpayable", "type": "address"}, "id": 46, "name": "ElementaryTypeName", "src": "658:7:1"}], "id": 47, "name": "VariableDeclaration", "src": "658:16:1"}, {"attributes": {"constant": false, "name": "amount", "scope": 86, "stateVariable": false, "storageLocation": "default", "type": "uint256", "value": null, "visibility": "internal"}, "children": [{"attributes": {"name": "uint256", "type": "uint256"}, "id": 48, "name": "ElementaryTypeName", "src": "676:7:1"}], "id": 49, "name": "VariableDeclaration", "src": "676:14:1"}], "id": 50, "name": "ParameterList", "src": "657:34:1"}, {"children": [{"attributes": {"constant": false, "name": "sufficient", "scope": 86, "stateVariable": false, "storageLocation": "default", "type": "bool", "value": null, "visibility": "internal"}, "children": [{"attributes": {"name": "bool", "type": "bool"}, "id": 51, "name": "ElementaryTypeName", "src": "726:4:1"}], "id": 52, "name": "VariableDeclaration", "src": "726:15:1"}], "id": 53, "name": "ParameterList", "src": "725:17:1"}, {"children": [{"attributes": {"falseBody": null}, "children": [{"attributes": {"argumentTypes": null, "commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "operator": "<", "type": "bool"}, "children": [{"attributes": {"argumentTypes": null, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "type": "uint256"}, "children": [{"attributes": {"argumentTypes": null, "overloadedDeclarations": [null], "referencedDeclaration": 23, "type": "mapping(address => uint256)", "value": "balances"}, "id": 54, "name": "Identifier", "src": "763:8:1"}, {"attributes": {"argumentTypes": null, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "member_name": "sender", "referencedDeclaration": null, "type": "address payable"}, "children": [{"attributes": {"argumentTypes": null, "overloadedDeclarations": [null], "referencedDeclaration": 193, "type": "msg", "value": "msg"}, "id": 55, "name": "Identifier", "src": "772:3:1"}], "id": 56, "name": "MemberAccess", "src": "772:10:1"}], "id": 57, "name": "IndexAccess", "src": "763:20:1"}, {"attributes": {"argumentTypes": null, "overloadedDeclarations": [null], "referencedDeclaration": 49, "type": "uint256", "value": "amount"}, "id": 58, "name": "Identifier", "src": "786:6:1"}], "id": 59, "name": "BinaryOperation", "src": "763:29:1"}, {"attributes": {"functionReturnParameters": 53}, "children": [{"attributes": {"argumentTypes": null, "hexvalue": "66616c7365", "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "subdenomination": null, "token": "bool", "type": "bool", "value": "false"}, "id": 60, "name": "Literal", "src": "801:5:1"}], "id": 61, "name": "Return", "src": "794:12:1"}], "id": 62, "name": "IfStatement", "src": "759:47:1"}, {"children": [{"attributes": {"argumentTypes": null, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "operator": "-=", "type": "uint256"}, "children": [{"attributes": {"argumentTypes": null, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": true, "type": "uint256"}, "children": [{"attributes": {"argumentTypes": null, "overloadedDeclarations": [null], "referencedDeclaration": 23, "type": "mapping(address => uint256)", "value": "balances"}, "id": 63, "name": "Identifier", "src": "817:8:1"}, {"attributes": {"argumentTypes": null, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "member_name": "sender", "referencedDeclaration": null, "type": "address payable"}, "children": [{"attributes": {"argumentTypes": null, "overloadedDeclarations": [null], "referencedDeclaration": 193, "type": "msg", "value": "msg"}, "id": 64, "name": "Identifier", "src": "826:3:1"}], "id": 65, "name": "MemberAccess", "src": "826:10:1"}], "id": 66, "name": "IndexAccess", "src": "817:20:1"}, {"attributes": {"argumentTypes": null, "overloadedDeclarations": [null], "referencedDeclaration": 49, "type": "uint256", "value": "amount"}, "id": 67, "name": "Identifier", "src": "841:6:1"}], "id": 68, "name": "Assignment", "src": "817:30:1"}], "id": 69, "name": "ExpressionStatement", "src": "817:30:1"}, {"children": [{"attributes": {"argumentTypes": null, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "operator": "+=", "type": "uint256"}, "children": [{"attributes": {"argumentTypes": null, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": true, "type": "uint256"}, "children": [{"attributes": {"argumentTypes": null, "overloadedDeclarations": [null], "referencedDeclaration": 23, "type": "mapping(address => uint256)", "value": "balances"}, "id": 70, "name": "Identifier", "src": "858:8:1"}, {"attributes": {"argumentTypes": null, "overloadedDeclarations": [null], "referencedDeclaration": 47, "type": "address", "value": "receiver"}, "id": 71, "name": "Identifier", "src": "867:8:1"}], "id": 72, "name": "IndexAccess", "src": "858:18:1"}, {"attributes": {"argumentTypes": null, "overloadedDeclarations": [null], "referencedDeclaration": 49, "type": "uint256", "value": "amount"}, "id": 73, "name": "Identifier", "src": "880:6:1"}], "id": 74, "name": "Assignment", "src": "858:28:1"}], "id": 75, "name": "ExpressionStatement", "src": "858:28:1"}, {"children": [{"attributes": {"argumentTypes": null, "isConstant": false, "isLValue": false, "isPure": false, "isStructConstructorCall": false, "lValueRequested": false, "names": [null], "type": "tuple()", "type_conversion": false}, "children": [{"attributes": {"argumentTypes": [{"typeIdentifier": "t_address_payable", "typeString": "address payable"}, {"typeIdentifier": "t_address", "typeString": "address"}, {"typeIdentifier": "t_uint256", "typeString": "uint256"}], "overloadedDeclarations": [null], "referencedDeclaration": 31, "type": "function (address,address,uint256)", "value": "Transfer"}, "id": 76, "name": "Identifier", "src": "902:8:1"}, {"attributes": {"argumentTypes": null, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "member_name": "sender", "referencedDeclaration": null, "type": "address payable"}, "children": [{"attributes": {"argumentTypes": null, "overloadedDeclarations": [null], "referencedDeclaration": 193, "type": "msg", "value": "msg"}, "id": 77, "name": "Identifier", "src": "911:3:1"}], "id": 78, "name": "MemberAccess", "src": "911:10:1"}, {"attributes": {"argumentTypes": null, "overloadedDeclarations": [null], "referencedDeclaration": 47, "type": "address", "value": "receiver"}, "id": 79, "name": "Identifier", "src": "923:8:1"}, {"attributes": {"argumentTypes": null, "overloadedDeclarations": [null], "referencedDeclaration": 49, "type": "uint256", "value": "amount"}, "id": 80, "name": "Identifier", "src": "933:6:1"}], "id": 81, "name": "FunctionCall", "src": "902:38:1"}], "id": 82, "name": "EmitStatement", "src": "897:43:1"}, {"attributes": {"functionReturnParameters": 53}, "children": [{"attributes": {"argumentTypes": null, "hexvalue": "74727565", "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "subdenomination": null, "token": "bool", "type": "bool", "value": "true"}, "id": 83, "name": "Literal", "src": "958:4:1"}], "id": 84, "name": "Return", "src": "951:11:1"}], "id": 85, "name": "Block", "src": "748:222:1"}], "id": 86, "name": "FunctionDefinition", "src": "640:330:1"}, {"attributes": {"documentation": null, "implemented": true, "isConstructor": false, "kind": "function", "modifiers": [null], "name": "faucetCoin", "scope": 141, "stateMutability": "nonpayable", "superFunction": null, "visibility": "public"}, "children": [{"children": [{"attributes": {"constant": false, "name": "receiver", "scope": 112, "stateVariable": false, "storageLocation": "default", "type": "address", "value": null, "visibility": "internal"}, "children": [{"attributes": {"name": "address", "stateMutability": "nonpayable", "type": "address"}, "id": 87, "name": "ElementaryTypeName", "src": "998:7:1"}], "id": 88, "name": "VariableDeclaration", "src": "998:16:1"}, {"attributes": {"constant": false, "name": "amount", "scope": 112, "stateVariable": false, "storageLocation": "default", "type": "uint256", "value": null, "visibility": "internal"}, "children": [{"attributes": {"name": "uint256", "type": "uint256"}, "id": 89, "name": "ElementaryTypeName", "src": "1016:7:1"}], "id": 90, "name": "VariableDeclaration", "src": "1016:14:1"}], "id": 91, "name": "ParameterList", "src": "997:34:1"}, {"children": [{"attributes": {"constant": false, "name": "sufficient", "scope": 112, "stateVariable": false, "storageLocation": "default", "type": "bool", "value": null, "visibility": "internal"}, "children": [{"attributes": {"name": "bool", "type": "bool"}, "id": 92, "name": "ElementaryTypeName", "src": "1066:4:1"}], "id": 93, "name": "VariableDeclaration", "src": "1066:15:1"}], "id": 94, "name": "ParameterList", "src": "1065:17:1"}, {"children": [{"children": [{"attributes": {"argumentTypes": null, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "operator": "+=", "type": "uint256"}, "children": [{"attributes": {"argumentTypes": null, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": true, "type": "uint256"}, "children": [{"attributes": {"argumentTypes": null, "overloadedDeclarations": [null], "referencedDeclaration": 23, "type": "mapping(address => uint256)", "value": "balances"}, "id": 95, "name": "Identifier", "src": "1099:8:1"}, {"attributes": {"argumentTypes": null, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "member_name": "sender", "referencedDeclaration": null, "type": "address payable"}, "children": [{"attributes": {"argumentTypes": null, "overloadedDeclarations": [null], "referencedDeclaration": 193, "type": "msg", "value": "msg"}, "id": 96, "name": "Identifier", "src": "1108:3:1"}], "id": 97, "name": "MemberAccess", "src": "1108:10:1"}], "id": 98, "name": "IndexAccess", "src": "1099:20:1"}, {"attributes": {"argumentTypes": null, "overloadedDeclarations": [null], "referencedDeclaration": 90, "type": "uint256", "value": "amount"}, "id": 99, "name": "Identifier", "src": "1123:6:1"}], "id": 100, "name": "Assignment", "src": "1099:30:1"}], "id": 101, "name": "ExpressionStatement", "src": "1099:30:1"}, {"children": [{"attributes": {"argumentTypes": null, "isConstant": false, "isLValue": false, "isPure": false, "isStructConstructorCall": false, "lValueRequested": false, "names": [null], "type": "tuple()", "type_conversion": false}, "children": [{"attributes": {"argumentTypes": [{"typeIdentifier": "t_address_payable", "typeString": "address payable"}, {"typeIdentifier": "t_address", "typeString": "address"}, {"typeIdentifier": "t_uint256", "typeString": "uint256"}], "overloadedDeclarations": [null], "referencedDeclaration": 31, "type": "function (address,address,uint256)", "value": "Transfer"}, "id": 102, "name": "Identifier", "src": "1145:8:1"}, {"attributes": {"argumentTypes": null, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "member_name": "sender", "referencedDeclaration": null, "type": "address payable"}, "children": [{"attributes": {"argumentTypes": null, "overloadedDeclarations": [null], "referencedDeclaration": 193, "type": "msg", "value": "msg"}, "id": 103, "name": "Identifier", "src": "1154:3:1"}], "id": 104, "name": "MemberAccess", "src": "1154:10:1"}, {"attributes": {"argumentTypes": null, "overloadedDeclarations": [null], "referencedDeclaration": 88, "type": "address", "value": "receiver"}, "id": 105, "name": "Identifier", "src": "1166:8:1"}, {"attributes": {"argumentTypes": null, "overloadedDeclarations": [null], "referencedDeclaration": 90, "type": "uint256", "value": "amount"}, "id": 106, "name": "Identifier", "src": "1176:6:1"}], "id": 107, "name": "FunctionCall", "src": "1145:38:1"}], "id": 108, "name": "EmitStatement", "src": "1140:43:1"}, {"attributes": {"functionReturnParameters": 94}, "children": [{"attributes": {"argumentTypes": null, "hexvalue": "74727565", "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "subdenomination": null, "token": "bool", "type": "bool", "value": "true"}, "id": 109, "name": "Literal", "src": "1201:4:1"}], "id": 110, "name": "Return", "src": "1194:11:1"}], "id": 111, "name": "Block", "src": "1088:125:1"}], "id": 112, "name": "FunctionDefinition", "src": "978:235:1"}, {"attributes": {"documentation": null, "implemented": true, "isConstructor": false, "kind": "function", "modifiers": [null], "name": "getBalanceInEth", "scope": 141, "stateMutability": "view", "superFunction": null, "visibility": "public"}, "children": [{"children": [{"attributes": {"constant": false, "name": "addr", "scope": 128, "stateVariable": false, "storageLocation": "default", "type": "address", "value": null, "visibility": "internal"}, "children": [{"attributes": {"name": "address", "stateMutability": "nonpayable", "type": "address"}, "id": 113, "name": "ElementaryTypeName", "src": "1246:7:1"}], "id": 114, "name": "VariableDeclaration", "src": "1246:12:1"}], "id": 115, "name": "ParameterList", "src": "1245:14:1"}, {"children": [{"attributes": {"constant": false, "name": "", "scope": 128, "stateVariable": false, "storageLocation": "default", "type": "uint256", "value": null, "visibility": "internal"}, "children": [{"attributes": {"name": "uint256", "type": "uint256"}, "id": 116, "name": "ElementaryTypeName", "src": "1281:7:1"}], "id": 117, "name": "VariableDeclaration", "src": "1281:7:1"}], "id": 118, "name": "ParameterList", "src": "1280:9:1"}, {"children": [{"attributes": {"functionReturnParameters": 118}, "children": [{"attributes": {"argumentTypes": null, "isConstant": false, "isLValue": false, "isPure": false, "isStructConstructorCall": false, "lValueRequested": false, "names": [null], "type": "uint256", "type_conversion": false}, "children": [{"attributes": {"argumentTypes": [{"typeIdentifier": "t_uint256", "typeString": "uint256"}, {"typeIdentifier": "t_rational_2_by_1", "typeString": "int_const 2"}], "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "member_name": "convert", "referencedDeclaration": 15, "type": "function (uint256,uint256) pure returns (uint256)"}, "children": [{"attributes": {"argumentTypes": null, "overloadedDeclarations": [null], "referencedDeclaration": 16, "type": "type(library ConvertLib)", "value": "ConvertLib"}, "id": 119, "name": "Identifier", "src": "1308:10:1"}], "id": 120, "name": "MemberAccess", "src": "1308:18:1"}, {"attributes": {"argumentTypes": null, "isConstant": false, "isLValue": false, "isPure": false, "isStructConstructorCall": false, "lValueRequested": false, "names": [null], "type": "uint256", "type_conversion": false}, "children": [{"attributes": {"argumentTypes": [{"typeIdentifier": "t_address", "typeString": "address"}], "overloadedDeclarations": [null], "referencedDeclaration": 140, "type": "function (address) view returns (uint256)", "value": "getBalance"}, "id": 121, "name": "Identifier", "src": "1327:10:1"}, {"attributes": {"argumentTypes": null, "overloadedDeclarations": [null], "referencedDeclaration": 114, "type": "address", "value": "addr"}, "id": 122, "name": "Identifier", "src": "1338:4:1"}], "id": 123, "name": "FunctionCall", "src": "1327:16:1"}, {"attributes": {"argumentTypes": null, "hexvalue": "32", "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "subdenomination": null, "token": "number", "type": "int_const 2", "value": "2"}, "id": 124, "name": "Literal", "src": "1345:1:1"}], "id": 125, "name": "FunctionCall", "src": "1308:39:1"}], "id": 126, "name": "Return", "src": "1301:46:1"}], "id": 127, "name": "Block", "src": "1290:65:1"}], "id": 128, "name": "FunctionDefinition", "src": "1221:134:1"}, {"attributes": {"documentation": null, "implemented": true, "isConstructor": false, "kind": "function", "modifiers": [null], "name": "getBalance", "scope": 141, "stateMutability": "view", "superFunction": null, "visibility": "public"}, "children": [{"children": [{"attributes": {"constant": false, "name": "addr", "scope": 140, "stateVariable": false, "storageLocation": "default", "type": "address", "value": null, "visibility": "internal"}, "children": [{"attributes": {"name": "address", "stateMutability": "nonpayable", "type": "address"}, "id": 129, "name": "ElementaryTypeName", "src": "1383:7:1"}], "id": 130, "name": "VariableDeclaration", "src": "1383:12:1"}], "id": 131, "name": "ParameterList", "src": "1382:14:1"}, {"children": [{"attributes": {"constant": false, "name": "", "scope": 140, "stateVariable": false, "storageLocation": "default", "type": "uint256", "value": null, "visibility": "internal"}, "children": [{"attributes": {"name": "uint256", "type": "uint256"}, "id": 132, "name": "ElementaryTypeName", "src": "1418:7:1"}], "id": 133, "name": "VariableDeclaration", "src": "1418:7:1"}], "id": 134, "name": "ParameterList", "src": "1417:9:1"}, {"children": [{"attributes": {"functionReturnParameters": 134}, "children": [{"attributes": {"argumentTypes": null, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "type": "uint256"}, "children": [{"attributes": {"argumentTypes": null, "overloadedDeclarations": [null], "referencedDeclaration": 23, "type": "mapping(address => uint256)", "value": "balances"}, "id": 135, "name": "Identifier", "src": "1445:8:1"}, {"attributes": {"argumentTypes": null, "overloadedDeclarations": [null], "referencedDeclaration": 130, "type": "address", "value": "addr"}, "id": 136, "name": "Identifier", "src": "1454:4:1"}], "id": 137, "name": "IndexAccess", "src": "1445:14:1"}], "id": 138, "name": "Return", "src": "1438:21:1"}], "id": 139, "name": "Block", "src": "1427:40:1"}], "id": 140, "name": "FunctionDefinition", "src": "1363:104:1"}], "id": 141, "name": "ContractDefinition", "src": "366:1104:1"}], "id": 142, "name": "SourceUnit", "src": "33:1439:1"}, "compiler": {"name": "solc", "version": "0.5.16+commit.9c3226ce.Emscripten.clang"}, "networks": {"1337": {"events": {}, "links": {"ConvertLib": "******************************************"}, "address": "******************************************", "transactionHash": "0xd2f749e421a9fdfb2333352b5e3ad496b7e48810dca158f9b50ed1d7d3b8fafd"}}, "schemaVersion": "3.4.3", "updatedAt": "2021-11-22T22:41:06.334Z", "networkType": "ethereum", "devdoc": {"methods": {}}, "userdoc": {"methods": {}}}