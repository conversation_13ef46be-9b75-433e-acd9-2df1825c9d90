import { CreditCardOutlined } from '@ant-design/icons';
import { Button, Input } from 'antd';
import Text from 'antd/lib/typography/Text';
import { useEffect, useState } from 'react';
import { useMoralis } from 'react-moralis';
import AddressInput from '../../AddressInput';
import AssetSelector from './AssetSelector';

const styles = {
  card: {
    alignItems: 'center',
    width: '100%',
  },
  header: {
    textAlign: 'center',
  },
  input: {
    width: '100%',
    outline: 'none',
    fontSize: '16px',
    whiteSpace: 'nowrap',
    overflow: 'hidden',
    textverflow: 'ellipsis',
    appearance: 'textfield',
    color: '#041836',
    fontWeight: '700',
    border: 'none',
    backgroundColor: 'transparent',
  },
  select: {
    marginTop: '20px',
    display: 'flex',
    alignItems: 'center',
  },
  textWrapper: { maxWidth: '80px', width: '100%' },
  row: {
    display: 'flex',
    alignItems: 'center',
    gap: '10px',
    flexDirection: 'row',
  },
};

function Transfer() {
  const { Moralis } = useMoralis();
  const [receiver, setReceiver] = useState();
  const [asset, setAsset] = useState();
  const [tx, setTx] = useState();
  const [amount, setAmount] = useState();
  const [isPending, setIsPending] = useState(false);

  useEffect(() => {
    asset && amount && receiver ? setTx({ amount, receiver, asset }) : setTx();
  }, [asset, amount, receiver]);

  async function transfer() {
    const { amount, receiver, asset } = tx;
    const options = {
      type: 'erc20',
      amount: Moralis.Units.Token(amount, asset.decimals),
      receiver,
      contractAddress: asset.token_address,
    };
    console.log(isPending);
    setIsPending(true);
    await Moralis.transfer(options)
      .then((tx) => {
        console.log(tx);
        setIsPending(false);
      })
      .catch((e) => {
        alert(e.message);
        setIsPending(false);
      });
  }

  return (
    <div style={styles.card}>
      <div style={styles.tranfer}>
        <div style={styles.header}>
          <h3>Transfer Assets</h3>
        </div>
        <div style={styles.select}>
          <div style={styles.textWrapper}>
            <Text strong>Address:</Text>
          </div>
          <AddressInput autoFocus onChange={setReceiver} />
        </div>
        <div style={styles.select}>
          <div style={styles.textWrapper}>
            <Text strong>Amount:</Text>
          </div>
          <Input
            size="large"
            prefix={<CreditCardOutlined />}
            onChange={(e) => {
              setAmount(`${e.target.value}`);
            }}
          />
        </div>
        <div style={styles.select}>
          <div style={styles.textWrapper}>
            <Text strong>Asset:</Text>
          </div>
          <AssetSelector setAsset={setAsset} style={{ width: '100%' }} />
        </div>
        <Button
          type="primary"
          size="large"
          loading={isPending}
          style={{ width: '100%', marginTop: '25px' }}
          onClick={() => transfer()}
          disabled={!tx}
        >
          Transfer💸
        </Button>
      </div>
    </div>
  );
}

export default Transfer;
