{"name": "project", "version": "1.0.8", "private": true, "type": "module", "dependencies": {"@ant-design/icons": "^4.7.0", "@emotion/react": "^11.6.0", "@emotion/styled": "^11.6.0", "@mui/icons-material": "^5.2.0", "@mui/lab": "^5.0.0-alpha.57", "@mui/material": "^5.2.1", "@testing-library/jest-dom": "^5.11.4", "@testing-library/react": "^11.1.0", "@testing-library/user-event": "^12.1.10", "@walletconnect/web3-provider": "^1.6.6", "@web3-react/core": "^6.1.9", "@web3-react/injected-connector": "^6.0.7", "@web3-react/walletconnect-connector": "^6.2.8", "antd": "^4.16.13", "axios": "^1.4.0", "chai": "4.2.0", "chai-as-promised": "7.1.1", "chai-bignumber": "3.0.0", "crypto": "^1.0.1", "ethers": "^5.5.2", "firebase": "^10.12.2", "fs": "^0.0.1-security", "geth": "^0.4.0", "mdbreact": "^5.1.0", "moment": "^2.29.1", "moralis": "^0.0.176", "papaparse": "^5.4.1", "path": "^0.12.7", "react": "^17.0.2", "react-blockies": "^1.4.1", "react-bootstrap": "1.0.0-beta.5", "react-copy-to-clipboard": "^5.0.4", "react-countdown": "^2.3.2", "react-dom": "^17.0.2", "react-icons": "^4.3.1", "react-moralis": "^0.3.0", "react-player": "^2.10.0", "react-router": "^5.2.1", "react-router-dom": "^5.3.0", "react-svg-config": "^1.0.1", "web-vitals": "^1.0.1", "web3": "1.0.0-beta.55", "yarn": "^1.22.17"}, "scripts": {"start": "vite", "build": "vite build", "serve": "vite preview", "app:pre": "vite", "test": "your-test-script-here", "deploy": "node Truffle/scripts/deployContract.js", "prepare": "npm run app:pre", "devchain": "node Truffle/scripts/devChain.js", "watch:events": "moralis-admin-cli add-contract", "connect": "moralis-admin-cli connect-local-devchain"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@esbuild-plugins/node-globals-polyfill": "^0.2.3", "@esbuild-plugins/node-modules-polyfill": "^0.2.2", "@vitejs/plugin-react": "^4.3.4", "@vitejs/plugin-react-swc": "^3.7.2", "rollup-plugin-polyfill-node": "^0.13.0", "vite": "^6.0.11"}}